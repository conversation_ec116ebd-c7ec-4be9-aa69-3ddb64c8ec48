#!/usr/bin/env python3
"""
图像生成功能诊断脚本

用于诊断 AI Gen Hub 系统的图像生成功能问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def check_google_ai_image_models():
    """检查 Google AI 供应商的图像生成模型配置"""
    print("🔍 检查 Google AI 供应商的图像生成模型配置...")
    
    try:
        # 直接导入配置模块
        sys.path.insert(0, str(project_root / "src" / "ai_gen_hub" / "providers"))
        
        from pydantic import BaseModel, Field, validator
        from pydantic_settings import BaseSettings, SettingsConfigDict
        from typing import List, Optional
        
        # 模拟 ProviderConfig 和相关类
        class ProviderConfig(BaseModel):
            api_keys: List[str] = Field(default_factory=list)
            enabled: bool = Field(True)
            
            @validator('api_keys', pre=True)
            def parse_api_keys(cls, v):
                if isinstance(v, str):
                    return [key.strip() for key in v.split(',') if key.strip()]
                return v
        
        class KeyManager:
            def __init__(self, settings):
                pass
        
        # 模拟 ModelType 枚举
        class ModelType:
            TEXT_GENERATION = "text_generation"
            IMAGE_GENERATION = "image_generation"
        
        # 创建 Google AI 供应商实例
        config = ProviderConfig(
            api_keys="test-key",
            enabled=True
        )
        
        # 模拟 GoogleAIProvider 的关键部分
        class MockGoogleAIProvider:
            def __init__(self, config, key_manager):
                self.name = "google_ai"
                self.config = config
                
                # 支持的模型类型
                self._supported_model_types = [
                    ModelType.TEXT_GENERATION,
                    ModelType.IMAGE_GENERATION
                ]
                
                # 支持的模型列表
                self._supported_models = [
                    # Gemini 2.5 系列
                    "gemini-2.5-pro",
                    "gemini-2.5-flash",
                    "gemini-2.5-flash-lite",
                    # Gemini 2.0 系列
                    "gemini-2.0-flash",
                    "gemini-2.0-flash-lite",
                    # Gemini 1.5 系列
                    "gemini-1.5-pro",
                    "gemini-1.5-flash",
                    "gemini-1.5-flash-8b",
                ]
                
                # 模型映射
                self._model_mapping = {
                    "gemini-latest": "gemini-2.5-flash",
                    "gemini": "gemini-2.5-flash",
                    "gemini-pro": "gemini-2.5-pro",
                }
            
            def supports_model_type(self, model_type):
                return model_type in self._supported_model_types
            
            def supports_model(self, model):
                return model in self._supported_models or model in self._model_mapping
            
            def map_model_name(self, model):
                return self._model_mapping.get(model, model)
            
            def get_default_image_model(self):
                # 这是问题所在：默认图像生成模型不在支持列表中
                return "gemini-2.0-flash-preview-image-generation"
        
        provider = MockGoogleAIProvider(config, None)
        
        print(f"✅ Google AI 供应商创建成功")
        print(f"   支持的模型类型: {provider._supported_model_types}")
        print(f"   支持图像生成: {provider.supports_model_type(ModelType.IMAGE_GENERATION)}")
        print()
        
        # 检查图像生成模型支持
        print("📋 图像生成模型检查:")
        default_image_model = provider.get_default_image_model()
        print(f"   默认图像生成模型: {default_image_model}")
        print(f"   是否在支持列表中: {provider.supports_model(default_image_model)}")
        print()
        
        # 检查支持的模型列表
        print("📝 支持的模型列表:")
        for model in provider._supported_models:
            print(f"   - {model}")
        print()
        
        # 检查图像相关的模型
        print("🖼️ 图像相关模型检查:")
        image_keywords = ["image", "dall", "vision", "2.0-flash"]
        for model in provider._supported_models:
            if any(keyword in model.lower() for keyword in image_keywords):
                print(f"   ✅ {model} (可能支持图像)")
        
        # 检查问题
        print("❌ 发现的问题:")
        if not provider.supports_model(default_image_model):
            print(f"   1. 默认图像生成模型 '{default_image_model}' 不在支持列表中")
            print(f"   2. 这会导致路由器无法找到支持该模型的供应商")
        
        return provider
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_image_service_model_filtering():
    """检查图像生成服务的模型过滤逻辑"""
    print("🔍 检查图像生成服务的模型过滤逻辑...")
    
    try:
        # 模拟 ImageGenerationService 的 get_supported_models 方法
        def get_supported_models_mock(provider_models):
            """模拟图像生成服务的模型过滤"""
            image_models = [
                model for model in provider_models
                if "dall" in model.lower() or "image" in model.lower()
            ]
            return image_models
        
        # 测试 Google AI 的模型列表
        google_ai_models = [
            "gemini-2.5-pro",
            "gemini-2.5-flash",
            "gemini-2.0-flash",
            "gemini-1.5-pro",
            "gemini-2.0-flash-preview-image-generation",  # 这个不在实际支持列表中
        ]
        
        filtered_models = get_supported_models_mock(google_ai_models)
        
        print(f"   原始模型列表: {google_ai_models}")
        print(f"   过滤后的图像模型: {filtered_models}")
        
        if not filtered_models:
            print("   ❌ 问题：没有找到图像生成模型")
            print("   原因：过滤逻辑只查找包含 'dall' 或 'image' 的模型名称")
            print("   但 Gemini 模型名称不包含这些关键词")
        
        return filtered_models
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []

def check_routing_logic():
    """检查路由逻辑"""
    print("🔍 检查路由逻辑...")
    
    try:
        # 模拟路由器的 get_available_providers 逻辑
        class MockProvider:
            def __init__(self, name, supported_types, supported_models):
                self.name = name
                self._supported_model_types = supported_types
                self._supported_models = supported_models
                self.status = "HEALTHY"
            
            def supports_model_type(self, model_type):
                return model_type in self._supported_model_types
            
            def supports_model(self, model):
                return model in self._supported_models
        
        # 创建模拟供应商
        google_ai = MockProvider(
            "google_ai",
            ["text_generation", "image_generation"],
            ["gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.0-flash"]
        )
        
        openai = MockProvider(
            "openai",
            ["text_generation", "image_generation"],
            ["gpt-4", "gpt-3.5-turbo", "dall-e-2", "dall-e-3"]
        )
        
        providers = [google_ai, openai]
        
        def get_available_providers_mock(model_type, model=None):
            """模拟获取可用供应商"""
            available = []
            for provider in providers:
                # 检查模型类型支持
                if not provider.supports_model_type(model_type):
                    continue
                
                # 检查特定模型支持
                if model and not provider.supports_model(model):
                    continue
                
                available.append(provider)
            
            return available
        
        # 测试不同的图像生成模型
        test_models = [
            "dall-e-3",  # OpenAI 模型
            "gemini-2.0-flash",  # Google AI 支持的模型
            "gemini-2.0-flash-preview-image-generation",  # Google AI 不支持的模型
        ]
        
        for model in test_models:
            available = get_available_providers_mock("image_generation", model)
            provider_names = [p.name for p in available]
            print(f"   模型 '{model}': 可用供应商 {provider_names}")
        
        # 测试不指定模型的情况
        available_all = get_available_providers_mock("image_generation")
        provider_names_all = [p.name for p in available_all]
        print(f"   不指定模型: 可用供应商 {provider_names_all}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 图像生成功能诊断开始")
    print("=" * 60)
    
    # 1. 检查 Google AI 图像生成模型配置
    provider = check_google_ai_image_models()
    print()
    
    # 2. 检查图像生成服务的模型过滤逻辑
    filtered_models = check_image_service_model_filtering()
    print()
    
    # 3. 检查路由逻辑
    routing_ok = check_routing_logic()
    print()
    
    print("=" * 60)
    print("🏁 诊断完成")
    
    print("\n📋 问题总结:")
    print("1. ❌ Google AI 默认图像生成模型不在支持列表中")
    print("   - 默认模型: 'gemini-2.0-flash-preview-image-generation'")
    print("   - 支持列表中没有包含此模型")
    print()
    
    print("2. ❌ 图像生成服务的模型过滤逻辑有问题")
    print("   - 只查找包含 'dall' 或 'image' 的模型名称")
    print("   - Gemini 模型名称不包含这些关键词")
    print()
    
    print("3. ❌ 路由器无法找到支持图像生成的供应商")
    print("   - 因为模型不在支持列表中，供应商被过滤掉")
    print()
    
    print("🔧 建议的修复方案:")
    print("1. 将图像生成模型添加到 Google AI 的支持列表中")
    print("2. 修改图像生成服务的模型过滤逻辑")
    print("3. 更新 Google AI 的模型映射配置")

if __name__ == "__main__":
    main()
