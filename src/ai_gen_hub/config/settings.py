"""
AI Gen Hub 配置管理

提供统一的配置管理功能，支持环境变量、配置文件、动态配置等多种配置源。
使用Pydantic进行配置验证和类型检查。
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录路径"""
    current_file = Path(__file__)
    # 从 src/ai_gen_hub/config/settings.py 向上找到项目根目录
    project_root = current_file.parent.parent.parent.parent
    return project_root

PROJECT_ROOT = get_project_root()


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = Field(..., description="数据库连接URL")
    pool_size: int = Field(10, description="连接池大小")
    max_overflow: int = Field(20, description="连接池最大溢出")
    pool_timeout: int = Field(30, description="连接池超时时间")
    echo: bool = Field(False, description="是否打印SQL语句")


class RedisConfig(BaseModel):
    """Redis配置"""
    url: str = Field("redis://localhost:6379/0", description="Redis连接URL")
    password: Optional[str] = Field(None, description="Redis密码")
    db: int = Field(0, description="Redis数据库编号")
    pool_size: int = Field(10, description="连接池大小")
    pool_timeout: int = Field(10, description="连接池超时时间")
    decode_responses: bool = Field(True, description="是否解码响应")


class ProviderConfig(BaseModel):
    """AI供应商配置"""
    api_keys: List[str] = Field(default_factory=list, description="API密钥列表")
    base_url: Optional[str] = Field(None, description="API基础URL")
    timeout: int = Field(60, description="请求超时时间")
    max_retries: int = Field(3, description="最大重试次数")
    retry_delay: float = Field(1.0, description="重试延迟时间")
    rate_limit: Optional[int] = Field(None, description="速率限制（请求/分钟）")
    enabled: bool = Field(True, description="是否启用该供应商")
    priority: int = Field(1, description="供应商优先级")
    weight: float = Field(1.0, description="负载均衡权重")
    
    @validator('api_keys', pre=True)
    def parse_api_keys(cls, v):
        """解析API密钥列表"""
        if isinstance(v, str):
            return [key.strip() for key in v.split(',') if key.strip()]
        return v


class CacheConfig(BaseModel):
    """缓存配置"""
    # 内存缓存配置
    memory_cache_size: int = Field(1000, description="内存缓存大小")
    memory_cache_ttl: int = Field(3600, description="内存缓存TTL（秒）")
    
    # Redis缓存配置
    redis_cache_ttl: int = Field(7200, description="Redis缓存TTL（秒）")
    redis_cache_prefix: str = Field("ai_gen_hub:cache:", description="Redis缓存键前缀")
    
    # 缓存策略
    enable_memory_cache: bool = Field(True, description="是否启用内存缓存")
    enable_redis_cache: bool = Field(True, description="是否启用Redis缓存")
    cache_compression: bool = Field(True, description="是否启用缓存压缩")


class MonitoringConfig(BaseModel):
    """监控配置"""
    # 日志配置
    log_level: str = Field("INFO", description="日志级别")
    log_format: str = Field("json", description="日志格式")
    log_file: Optional[str] = Field(None, description="日志文件路径")
    
    # Prometheus配置
    prometheus_enabled: bool = Field(True, description="是否启用Prometheus监控")
    prometheus_port: int = Field(9090, description="Prometheus端口")
    
    # 健康检查配置
    health_check_interval: int = Field(60, description="健康检查间隔（秒）")
    health_check_timeout: int = Field(10, description="健康检查超时（秒）")
    
    # 指标收集
    collect_detailed_metrics: bool = Field(True, description="是否收集详细指标")
    metrics_retention_days: int = Field(30, description="指标保留天数")


class SecurityConfig(BaseModel):
    """安全配置"""
    # JWT配置
    jwt_secret_key: str = Field("ai-gen-hub-default-secret-key-change-in-production", description="JWT密钥")
    jwt_algorithm: str = Field("HS256", description="JWT算法")
    jwt_expire_minutes: int = Field(1440, description="JWT过期时间（分钟）")
    
    # API密钥
    api_key: Optional[str] = Field(None, description="API密钥")
    
    # CORS配置
    cors_origins: List[str] = Field(default_factory=list, description="CORS允许的源")
    cors_allow_credentials: bool = Field(True, description="CORS是否允许凭证")
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        """解析CORS源列表"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',') if origin.strip()]
        return v


class StorageConfig(BaseModel):
    """存储配置"""
    # 本地存储
    local_storage_path: str = Field("./storage", description="本地存储路径")
    
    # S3配置
    s3_bucket: Optional[str] = Field(None, description="S3存储桶")
    s3_region: Optional[str] = Field(None, description="S3区域")
    s3_access_key: Optional[str] = Field(None, description="S3访问密钥")
    s3_secret_key: Optional[str] = Field(None, description="S3密钥")
    s3_endpoint_url: Optional[str] = Field(None, description="S3端点URL")
    
    # MinIO配置
    minio_endpoint: Optional[str] = Field(None, description="MinIO端点")
    minio_access_key: Optional[str] = Field(None, description="MinIO访问密钥")
    minio_secret_key: Optional[str] = Field(None, description="MinIO密钥")
    minio_bucket: Optional[str] = Field(None, description="MinIO存储桶")
    minio_secure: bool = Field(True, description="MinIO是否使用HTTPS")


class PerformanceConfig(BaseModel):
    """性能配置"""
    # 请求限制
    rate_limit_requests: int = Field(100, description="速率限制请求数")
    rate_limit_window: int = Field(60, description="速率限制时间窗口（秒）")
    
    # 超时配置
    request_timeout: int = Field(300, description="请求超时时间（秒）")
    stream_timeout: int = Field(600, description="流式请求超时时间（秒）")
    
    # 并发配置
    max_concurrent_requests: int = Field(100, description="最大并发请求数")
    max_queue_size: int = Field(1000, description="最大队列大小")
    
    # 重试配置
    default_max_retries: int = Field(3, description="默认最大重试次数")
    default_retry_delay: float = Field(1.0, description="默认重试延迟（秒）")
    exponential_backoff: bool = Field(True, description="是否使用指数退避")


class FeatureFlags(BaseModel):
    """功能开关"""
    enable_text_generation: bool = Field(True, description="启用文本生成")
    enable_image_generation: bool = Field(True, description="启用图像生成")
    enable_streaming: bool = Field(True, description="启用流式输出")
    enable_caching: bool = Field(True, description="启用缓存")
    enable_monitoring: bool = Field(True, description="启用监控")
    enable_rate_limiting: bool = Field(True, description="启用速率限制")
    enable_load_balancing: bool = Field(True, description="启用负载均衡")
    enable_circuit_breaker: bool = Field(True, description="启用熔断器")


class Settings(BaseSettings):
    """主配置类"""
    model_config = SettingsConfigDict(
        env_file=[
            PROJECT_ROOT / ".env",  # 项目根目录的 .env 文件
            ".env",  # 当前目录的 .env 文件（备用）
        ],
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # 基础配置
    environment: str = Field("development", description="运行环境")
    app_name: str = Field("AI Gen Hub", description="应用名称")
    app_version: str = Field("0.1.0", description="应用版本")
    debug: bool = Field(False, description="调试模式")
    
    # API配置
    api_host: str = Field("0.0.0.0", description="API主机")
    api_port: int = Field(8000, description="API端口")
    api_workers: int = Field(1, description="API工作进程数")
    
    # 数据库配置
    database: Optional[DatabaseConfig] = None
    
    # Redis配置
    redis: RedisConfig = Field(default_factory=RedisConfig)
    
    # AI供应商配置
    openai: ProviderConfig = Field(default_factory=ProviderConfig)
    google_ai: ProviderConfig = Field(default_factory=ProviderConfig)
    anthropic: ProviderConfig = Field(default_factory=ProviderConfig)
    dashscope: ProviderConfig = Field(default_factory=ProviderConfig)
    azure: ProviderConfig = Field(default_factory=ProviderConfig)
    
    # 其他配置
    cache: CacheConfig = Field(default_factory=CacheConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    storage: StorageConfig = Field(default_factory=StorageConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    features: FeatureFlags = Field(default_factory=FeatureFlags)
    
    @validator('database', pre=True)
    def parse_database_config(cls, v):
        """解析数据库配置"""
        if isinstance(v, str):
            return DatabaseConfig(url=v)
        elif isinstance(v, dict):
            return DatabaseConfig(**v)
        return v
    
    def get_provider_config(self, provider_name: str) -> Optional[ProviderConfig]:
        """获取指定供应商的配置
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            供应商配置或None
        """
        provider_configs = {
            "openai": self.openai,
            "google_ai": self.google_ai,
            "anthropic": self.anthropic,
            "dashscope": self.dashscope,
            "azure": self.azure,
        }
        return provider_configs.get(provider_name.lower())
    
    def get_enabled_providers(self) -> List[str]:
        """获取启用的供应商列表
        
        Returns:
            启用的供应商名称列表
        """
        enabled_providers = []
        for name, config in [
            ("openai", self.openai),
            ("google_ai", self.google_ai),
            ("anthropic", self.anthropic),
            ("dashscope", self.dashscope),
            ("azure", self.azure),
        ]:
            if config.enabled and config.api_keys:
                enabled_providers.append(name)
        return enabled_providers


# 全局配置实例
_settings: Optional[Settings] = None


def get_settings(config_path: Optional[str] = None, debug_logging: bool = False) -> Settings:
    """获取配置实例

    Args:
        config_path: 配置文件路径
        debug_logging: 是否启用调试日志

    Returns:
        配置实例
    """
    global _settings

    if _settings is None:
        if debug_logging:
            print(f"🔧 正在加载配置...")
            print(f"   项目根目录: {PROJECT_ROOT}")
            print(f"   .env 文件路径: {PROJECT_ROOT / '.env'}")
            print(f"   .env 文件存在: {(PROJECT_ROOT / '.env').exists()}")
            if config_path:
                print(f"   指定配置文件: {config_path}")

        if config_path and Path(config_path).exists():
            # 从配置文件加载
            if debug_logging:
                print(f"   从指定配置文件加载: {config_path}")
            _settings = Settings(_env_file=config_path)
        else:
            # 从环境变量和默认 .env 文件加载
            if debug_logging:
                print(f"   从默认配置加载")
            _settings = Settings()

        if debug_logging:
            print(f"✅ 配置加载完成:")
            print(f"   环境: {_settings.environment}")
            print(f"   调试模式: {_settings.debug}")
            print(f"   应用名称: {_settings.app_name}")

    return _settings


def reload_settings(config_path: Optional[str] = None, debug_logging: bool = False) -> Settings:
    """重新加载配置

    Args:
        config_path: 配置文件路径
        debug_logging: 是否启用调试日志

    Returns:
        新的配置实例
    """
    global _settings
    _settings = None
    return get_settings(config_path, debug_logging)
